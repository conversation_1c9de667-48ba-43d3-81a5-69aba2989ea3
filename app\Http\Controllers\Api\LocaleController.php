<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class LocaleController extends Controller
{
    function download(Request $request, $locale){
        $password = $request->input('password');
        if($password != "32c4581a-82e0-478b-96c7-f8566cdebdab"){
            return api_response_json(false, "Invalid password.", null, 401);
        }
        $path = resource_path("lang/{$locale}.json");
        if (!file_exists($path)) {
            abort(404);
        }
        return response()->download($path);
    }
}
