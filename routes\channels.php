<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Private chat channels for typing indicators
Broadcast::channel('private-luxustars-chat-{conversationId}', function ($user, $conversationId) {
    // Check if user is part of this conversation
    $conversation = \App\Models\Conversation::where('ids', $conversationId)->first();

    if (!$conversation) {
        return false;
    }

    // Allow access if user is either sender or receiver
    return $conversation->sender_id === $user->id || $conversation->receiver_id === $user->id;
});
