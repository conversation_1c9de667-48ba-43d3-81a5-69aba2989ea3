<?php

use App\Http\Controllers\Api\{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,AuthController, BookingController, LocaleController};
use App\Http\Controllers\Api\CalendarController;
use App\Http\Controllers\Api\CardController;
use App\Http\Controllers\Api\CartController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\ListingController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\ShuftiController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\VerificationController;
use App\Http\Controllers\StripeIdentityController;
use App\Http\Controllers\WishlistController;
use Illuminate\Support\Facades\Route;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/user', function (Request $request) {
//     return $request->user();
// });



Route::prefix("{locale}")->group(function () {
    // Login 
    Route::prefix("auth")->group(function () {
        Route::post('/register', [AuthController::class, 'createUser']);
        Route::post('/login', [AuthController::class, 'loginUser']);
        // ->middleware("throttle:5,1");


        Route::post('check-email-otp', [AuthController::class, 'checkEmailOtp']);
        // ->middleware("throttle:5,1");
        Route::post('resend-email-otp', [AuthController::class, 'resendEmailOtp']);

        // Password Reset
        Route::post("check-password-otp", [AuthController::class, 'check_password_otp']);
        Route::post("forget-password", [AuthController::class, 'forgetPassword']);
        Route::post("reset-password", [AuthController::class, 'resetPassword']);
    });



    /**
     * // ====================================================== //
     *                  Guest Route
     * // ====================================================== //
     */

    // Download Locale
    Route::get("download-locale", [LocaleController::class, "download"]);

    // Home Listing
    Route::get("category-listings", [ListingController::class, "get_category_listings"]);
    Route::get("search-listing-name", [ListingController::class, "get_listings_by_name"]);
    Route::get("listing-detail/{listing_id}", [ListingController::class, "listing_detail"]);
    // Route::get("/listing/{locale}/{listing_id}/availability", [ListingController::class, "detail_with_availability"])->where("listing_id", "[0-9]+");

    // terms and condition
    Route::get("privacy-policy", [ApiController::class, "privacy"]);
    Route::get("terms-condition", [ApiController::class, "terms"]);
    // Category 
    Route::get("category", [CategoryController::class, "get_category"]);

    /**
     * // ====================================================== //
     *                  Guest Route END
     * // ====================================================== //
     */



    Route::middleware("auth:sanctum")->group(function () {

        // User
        Route::get("user/me", [ApiController::class, "userMe"]);
        Route::post("switch-role", [ApiController::class, "switch_role"]);
        // User End

        // Shufti
        Route::get("verification-link", [ShuftiController::class, "get_verification_link"]);
        // Shufti End

        // verification otp check 
        Route::post("send-otp", [VerificationController::class, "resend_otp"]);
        Route::post("check-phone-otp", [VerificationController::class, "check_phone_otp"]);
        Route::post("check-email-otp", [VerificationController::class, "check_email_otp"]);

        /**
         * // ====================================================== //
         *                  SERVICE PROVIDER ROUTES
         * // ====================================================== //
         */
        Route::middleware("roles:service")->group(function () {

            // listing Route
            Route::post("listing-consent", [ListingController::class, "listing_consent"]);

            Route::group(['prefix' => 'listings', 'middleware' => ["listingConsent"]], function () {
                Route::get("/", [ListingController::class, "my_listing"]);
                Route::get("create/{category_id}", [ListingController::class, "create_listing"]);
                Route::post("create-update/{category_ids}/{listing_ids?}", [ListingController::class, "store_update_listing"]);
                Route::get("/show/{listing_id}", [ListingController::class, "show_listing"]);
                Route::post("media-upload/{listing_id}", [ListingController::class, "listingMedia"]);
                Route::post("duplicate/{listing_id}", [ListingController::class, "duplicate_listing"]);
                Route::post("image-delete", [ListingController::class, "imageDelete"]);
                Route::post("image-sorting", [ListingController::class, "sortingImage"]);
                Route::post("listing-cover-image", [ListingController::class, "coverImage"]);
                Route::post("internal-name", [ListingController::class, "internal_name"]);
                Route::post("pause-listing/{ids}", [ListingController::class, "pause_listing"]);
                Route::get("short-url/{listing_id}", [ListingController::class, "short_url"]);
                Route::post("delete/{ids}", [ListingController::class, "delete_listing"]);
            });
            // Listing end

            // calendar 
            Route::prefix("calendar")->group(function () {
                Route::get("blocked-dates/{listing_ids?}", [CalendarController::class, "get_blocked_dates"]);
                Route::post("block-date", [CalendarController::class, "block_date"]);
                Route::post("unblock-date", [CalendarController::class, "unblock_date"]);
                Route::get("download-ics", [CalendarController::class, "downloadIcs"]);
            });
            // calendar 



            Route::prefix("bookings")->group(function () {
                Route::get("provider", [BookingController::class, "get_provider_bookings"]);
                Route::get("download-csv", [BookingController::class, "download_csv"]);
                // Route::get("provider", [BookingController::class, "get_provider_bookings"]);    // Provider bookings
                // Route::post("reserve", [BookingController::class, "reserve_booking"]);
                // Route::get("listing/{ids}", [BookingController::class, "listing_booking"]);
            });
        });
        /**
         * // ====================================================== //
         *                SERVICE PROVIDER ROUTES END
         * // ====================================================== //
         */

        Route::post("auth/logout", [AuthController::class, "logout"]);
    });
});


// Route::post('/auth/register', [AuthController::class, 'createUser']);
// Route::post('/auth/login', [AuthController::class, 'loginUser']);
// email verification
// Route::post("resend-otp", [AuthController::class, "resend_otp"]);
// Route::post("check-otp", [AuthController::class, "check_otp"]);
Route::post("email-verification", [AuthController::class, "email_verification"]);
// password reset
Route::post("forget-password", [AuthController::class, "forget_password"]);
Route::post("reset-password", [AuthController::class, "reset_password"]);

// Route::get("/all-listings", [ListingController::class, "get_listing"]);
Route::get("/listing", [ListingController::class, "get_category_listing"]);


// category
Route::get("category/{locale}", [CategoryController::class, "get_category"]);

Route::prefix("policy")->group(function () {
    Route::get("terms-condition", [ApiController::class, "terms"]);
    Route::get("privacy-policy", [ApiController::class, "privacy"]);
    Route::get("faq", [ApiController::class, "faq"]);
});

// review get
Route::get("review/{listing_id}", [ReviewController::class, "get_review_listing"]);

Route::middleware('auth:sanctum')->group(function () {
    Route::get("/listings", [ListingController::class, "get_category_listing"]);
    Route::get("/listings/{listing_id}", [ListingController::class, "listings_detail"]);

    // profile
    Route::get("profile", [UserController::class, "get_profile"]);
    Route::post("update-profile", [UserController::class, "update_profile"]);
    Route::post("update-password", [UserController::class, "update_password"]);
    Route::delete("delete-profile", [UserController::class, "delete_profile"]);
    Route::get("users", [UserController::class, "index"]);
    Route::get("userinfo", [UserController::class, "user"]);

    // home page
    Route::get("home", [ListingController::class, "get_listing"]);

    // listing 
    Route::get("my-listings", [ListingController::class, "my_listing"]);
    Route::group(['prefix' => 'listing'], function () {
        Route::post("image-upload", [ListingController::class, "image_upload"]);
        // Route::get("/", [ListingController::class, "get_listing"]);
        Route::post("store", [ListingController::class, "add_listing"]);
        Route::post("update", [ListingController::class, "update_listing"]);
        Route::delete("delete/{listing_id}", [ListingController::class, "delete_listing"]);
    });
    Route::get("amenity", [ListingController::class, "get_amenities"]);

    // kyc 
    Route::get("kyc-link", [StripeIdentityController::class, "get_kyc_link"]);

    // Wishlist
    Route::group(['prefix' => 'wishlist'], function () {
        Route::get("/", [WishlistController::class, "get_wishlist"]);
        Route::post("/add", [WishlistController::class, "add_wishlist"]);
    });

    // Cart
    Route::group(['prefix' => 'cart'], function () {
        Route::get("/", [CartController::class, "get_cart"]);
        Route::post("/add", [CartController::class, "add_cart"]);
        Route::get("/delete/{cart_id}", [CartController::class, "delete_cart"]);
        Route::post("checkout", [CartController::class, "checkout"]);
    });

    // card
    Route::group(["prefix" => "card"], function () {
        Route::get("/", [CardController::class, "get_card"]);
        Route::post("/add", [CardController::class, "add_card"]);
        Route::get("/default/{id}", [CardController::class, "set_default_card"]);
    });

    // booking
    // Route::prefix('booking')->group(function () {
    //     Route::get("/", [BookingController::class, "get_bookings"]);
    //     Route::get("provider", [BookingController::class, "get_provider_bookings"]);
    //     Route::post("reserve", [BookingController::class, "reserve_booking"]);
    //     Route::get("listing/{id}", [BookingController::class, "listing_booking"]);
    // });

    Route::prefix("calendar")->group(function () {
        Route::post("reserve", [CalendarController::class, "reserve_calender"]);
        Route::get("reserve-dates/{listing_id}", [CalendarController::class, "listing_reserve_date"]);
    });

    // review add
    Route::post("add-review", [ReviewController::class, "review_post"]);

    Route::prefix("notification")->group(function () {
        Route::get("/", [NotificationController::class, "get_notification"]);
        Route::post("/read", [NotificationController::class, "read_notification"]);
    });
});
